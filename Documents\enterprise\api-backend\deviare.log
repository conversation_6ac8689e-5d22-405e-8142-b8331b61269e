
 INFO 2025-08-28 10:49:04,145 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-08-28 10:49:19,489 views 15732 === SUBSCRIPTIONFLOW WEBHOOK RECEIVED === 


 INFO 2025-08-28 10:49:19,491 views 15733 Headers: {'Static-Token': 'deviareLg9VwCn6J1lVkPxcAYJqnd-academyADbybpNvp34JSrmnYatHyUme', 'Content-Length': '11954', 'Content-Type': 'application/x-www-form-urlencoded', 'User-Agent': 'GuzzleHttp/7', 'Host': 'webhook.site', 'Accept': '*/*', 'Accept-Encoding': 'gzip, deflate, br', 'Connection': 'keep-alive'} 


 INFO 2025-08-28 10:49:19,491 views 15734 Content Type: application/x-www-form-urlencoded 


 INFO 2025-08-28 10:49:19,496 views 15795 === FORM DATA DEBUG === 


 INFO 2025-08-28 10:49:19,496 views 15796 Total parameters received: 169 


 INFO 2025-08-28 10:49:19,507 views 15798 Parameter: type = invoices 


 INFO 2025-08-28 10:49:19,507 views 15798 Parameter: id = 4b8af598-3965-485c-b333-187e2f2ed3c0 


 INFO 2025-08-28 10:49:19,507 views 15798 Parameter: attributes[id] = 4b8af598-3965-485c-b333-187e2f2ed3c0 


 INFO 2025-08-28 10:49:19,508 views 15798 Parameter: attributes[name] = IN-25 


 INFO 2025-08-28 10:49:19,508 views 15798 Parameter: attributes[invoice_date] = 2025-08-27 


 INFO 2025-08-28 10:49:19,508 views 15798 Parameter: attributes[due_date] = 2025-08-27 


 INFO 2025-08-28 10:49:19,509 views 15798 Parameter: attributes[terms] =  


 INFO 2025-08-28 10:49:19,509 views 15798 Parameter: attributes[status] = Due 


 INFO 2025-08-28 10:49:19,510 views 15798 Parameter: attributes[discount_value] = 0 


 INFO 2025-08-28 10:49:19,510 views 15798 Parameter: attributes[tax_amount] = 12.913043478261 


 INFO 2025-08-28 10:49:19,510 views 15801 Raw event field: NOT_FOUND 


 INFO 2025-08-28 10:49:19,511 views 15802 Raw method field: NOT_FOUND 


 INFO 2025-08-28 10:49:19,511 views 15803 Raw type field: invoices 


 INFO 2025-08-28 10:49:19,511 views 15808 Keys containing 'event': [] 


 INFO 2025-08-28 10:49:19,511 views 15809 Keys containing 'method': [] 


 INFO 2025-08-28 10:49:19,511 views 15813 Last 5 parameters: ['relationships[created_by][link]', 'relationships[updated_by][id]', 'relationships[updated_by][link]', 'relationships[assigned_to][id]', 'relationships[assigned_to][link]'] 


 INFO 2025-08-28 10:49:19,512 views 15815 Last param: relationships[created_by][link] = http://localhost/subscriptionflow.com/api/v1/users/adb17978-041c-40de-9be1-4f11283eaacc 


 INFO 2025-08-28 10:49:19,512 views 15815 Last param: relationships[updated_by][id] = adb17978-041c-40de-9be1-4f11283eaacc 


 INFO 2025-08-28 10:49:19,512 views 15815 Last param: relationships[updated_by][link] = http://localhost/subscriptionflow.com/api/v1/users/adb17978-041c-40de-9be1-4f11283eaacc 


 INFO 2025-08-28 10:49:19,512 views 15815 Last param: relationships[assigned_to][id] = adb17978-041c-40de-9be1-4f11283eaacc 


 INFO 2025-08-28 10:49:19,512 views 15815 Last param: relationships[assigned_to][link] = http://localhost/subscriptionflow.com/api/v1/us 


 INFO 2025-08-28 10:49:19,513 views 15961 Successfully parsed form-encoded data with 30 attributes 


 INFO 2025-08-28 10:49:19,513 views 15962 Parsed invoice ID: 4b8af598-3965-485c-b333-187e2f2ed3c0 


 INFO 2025-08-28 10:49:19,513 views 15963 Parsed invoice status: Due 


 INFO 2025-08-28 10:49:19,513 views 15964 Parsed customer email: <EMAIL> 


 INFO 2025-08-28 10:49:19,513 views 15738 Processing form-encoded webhook data 


 INFO 2025-08-28 10:49:19,514 views 15748 Webhook Type: invoices 


 INFO 2025-08-28 10:49:19,514 views 15749 Webhook Event: unknown 


 INFO 2025-08-28 10:49:19,514 views 15750 Webhook Method: unknown 


 INFO 2025-08-28 10:49:19,514 views 15751 Invoice Status: Due 


 INFO 2025-08-28 10:49:19,515 views 15752 Invoice ID: 4b8af598-3965-485c-b333-187e2f2ed3c0 


 INFO 2025-08-28 10:49:19,515 views 15759 Event was unknown, defaulting to 'created' for invoice processing 


 INFO 2025-08-28 10:49:19,516 views 15995 Invoice ID from last_activity: 4b8af598-3965-485c-b333-187e2f2ed3c0 


 INFO 2025-08-28 10:49:27,039 views 16007 SUCCESS: Fetched complete invoice data from SubscriptionFlow API 


 INFO 2025-08-28 10:49:32,426 views 16166 SUCCESS: Created Invoice in database: 


 INFO 2025-08-28 10:49:32,426 views 16193 Processing 1 subscription item(s): 


 INFO 2025-08-28 10:49:32,427 views 16197    Subscription ID: c6276acd-158a-43f6-8ba4-22b67866b514 


 INFO 2025-08-28 10:49:37,789 views 16372 SUCCESS: Fetched complete subscription data from SubscriptionFlow API 


 INFO 2025-08-28 10:49:40,047 views 16480 SUCCESS: Created Subscription in database: 


 INFO 2025-08-28 10:49:40,047 views 16491 Subscription includes 1 item(s): 


 INFO 2025-08-28 10:49:40,047 views 16493    Plan: Flexible - Basic | Product: Amazon Transcribe Getting Started 


 INFO 2025-08-28 10:49:40,048 views 16205 Processing customer: e70fc198-78f9-42a3-8015-d4f7891388a0 


 INFO 2025-08-28 10:49:43,192 views 16519 === Fetching customer details from: https://deviare.subscriptionflow.com/api/v1/customers/e70fc198-78f9-42a3-8015-d4f7891388a0 


 INFO 2025-08-28 10:49:45,612 views 16529 SUCCESS: Fetched complete customer data from SubscriptionFlow API 


 INFO 2025-08-28 10:49:47,887 views 16622 SUCCESS: Created Customer in database: 


 INFO 2025-08-28 10:49:47,887 views 16634    Billing: 33 Ballyclare Drive, Sandton, ZA 


 INFO 2025-08-28 10:49:47,887 views 16636    Shipping: 33 Ballyclare Drive, Sandton, ZA 


 INFO 2025-08-28 10:49:47,888 views 15763 PROCESSED INVOICE CREATED: 4b8af598-3965-485c-b333-187e2f2ed3c0 


 INFO 2025-08-28 10:49:47,888 views 15779 SubscriptionFlow webhook processed successfully 

