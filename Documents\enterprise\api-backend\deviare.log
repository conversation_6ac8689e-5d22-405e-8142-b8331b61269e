
 INFO 2025-08-28 00:15:01,652 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-08-28 00:15:31,897 views 15732 === SUBSCRIPTIONFLOW WEBHOOK RECEIVED === 


 INFO 2025-08-28 00:15:31,897 views 15733 Headers: {'Static-Token': 'deviareLg9VwCn6J1lVkPxcAYJqnd-academyADbybpNvp34JSrmnYatHyUme', 'Content-Length': '5304', 'Content-Type': 'application/x-www-form-urlencoded', 'User-Agent': 'GuzzleHttp/7', 'Host': 'webhook.site', 'Accept': '*/*', 'Accept-Encoding': 'gzip, deflate, br', 'Connection': 'keep-alive'} 


 INFO 2025-08-28 00:15:31,897 views 15734 Content Type: application/x-www-form-urlencoded 


 INFO 2025-08-28 00:15:31,897 views 15838 Successfully parsed form data into structured format 


 INFO 2025-08-28 00:15:31,941 views 15740 Parsed form-encoded webhook data 


 INFO 2025-08-28 00:15:31,941 views 15763 Webhook Type: transactions 


 INFO 2025-08-28 00:15:31,941 views 15764 Webhook Event: unknown 


 INFO 2025-08-28 00:15:31,941 views 15765 Webhook Method: unknown 


 INFO 2025-08-28 00:15:31,941 views 15766 Data Status: Pending 


 INFO 2025-08-28 00:15:31,941 views 15767 Data ID: 5717037d-b280-4b50-89ef-e6bb84788725 


 WARNING 2025-08-28 00:15:31,941 views 15775 UNHANDLED TRANSACTION EVENT: unknown 


 INFO 2025-08-28 00:15:31,941 views 15795 SubscriptionFlow webhook processed successfully 


 INFO 2025-08-28 00:17:40,251 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 INFO 2025-08-28 00:17:52,062 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-08-28 00:18:13,456 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 INFO 2025-08-28 00:18:24,772 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-08-28 00:18:59,987 views 15732 === SUBSCRIPTIONFLOW WEBHOOK RECEIVED === 


 INFO 2025-08-28 00:18:59,987 views 15733 Headers: {'Static-Token': 'deviareLg9VwCn6J1lVkPxcAYJqnd-academyADbybpNvp34JSrmnYatHyUme', 'Content-Length': '387', 'Content-Type': 'application/x-www-form-urlencoded', 'Host': '127.0.0.1:8000', 'User-Agent': 'TestScript/1.0', 'Accept-Encoding': 'gzip, deflate', 'Accept': '*/*', 'Connection': 'keep-alive'} 


 INFO 2025-08-28 00:18:59,987 views 15734 Content Type: application/x-www-form-urlencoded 


 INFO 2025-08-28 00:18:59,987 views 15839 Successfully parsed form data into structured format 


 INFO 2025-08-28 00:18:59,987 views 15740 Parsed form-encoded webhook data 


 INFO 2025-08-28 00:18:59,987 views 15763 Webhook Type: transactions 


 INFO 2025-08-28 00:18:59,987 views 15764 Webhook Event: created 


 INFO 2025-08-28 00:18:59,987 views 15765 Webhook Method: POST 


 INFO 2025-08-28 00:18:59,987 views 15766 Data Status: Pending 


 INFO 2025-08-28 00:18:59,987 views 15767 Data ID: test-transaction-123 


 INFO 2025-08-28 00:18:59,987 views 15768 Main data keys: ['id', 'name', 'status', 'amount', 'currency', 'customer'] 


 INFO 2025-08-28 00:18:59,987 views 15909 === PROCESSING TRANSACTION CREATED === 


 INFO 2025-08-28 00:18:59,987 views 15910 Transaction ID: test-transaction-123 


 INFO 2025-08-28 00:18:59,987 views 15911 Transaction Status: Pending 


 INFO 2025-08-28 00:18:59,987 views 15912 Transaction Amount: 100 USD 


 INFO 2025-08-28 00:18:59,987 views 15913 Transaction data available: True 


 INFO 2025-08-28 00:18:59,987 views 15914 Transaction data type: <class 'dict'> 


 INFO 2025-08-28 00:18:59,987 views 15917 About to save transaction to database... 


 INFO 2025-08-28 00:18:59,987 views 16232 === SAVING TRANSACTION TO DATABASE === 


 INFO 2025-08-28 00:18:59,987 views 16233 Transaction ID: test-transaction-123 


 INFO 2025-08-28 00:18:59,987 views 16234 Transaction Name: TEST-TRANSACTION 


 INFO 2025-08-28 00:18:59,987 views 16235 Status: Pending 


 INFO 2025-08-28 00:18:59,987 views 16236 Amount: 100 


 INFO 2025-08-28 00:18:59,987 views 16237 Currency: USD 


 INFO 2025-08-28 00:18:59,987 views 16238 Transaction data keys: ['id', 'name', 'status', 'amount', 'currency', 'customer'] 


 ERROR 2025-08-28 00:19:05,742 views 16314 ERROR: Error saving transaction to database: (1048, "Column 'date' cannot be null") 
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 916, in get_or_create
    return self.get(**kwargs), False
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 637, in get
    raise self.model.DoesNotExist(
main.models.SubscriptionTransaction.DoesNotExist: SubscriptionTransaction matching query does not exist.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\MySQLdb\cursors.py", line 206, in execute
    res = self._query(query)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\MySQLdb\cursors.py", line 319, in _query
    db.query(q)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\MySQLdb\connections.py", line 254, in query
    _mysql.connection.query(self, query)
MySQLdb.OperationalError: (1048, "Column 'date' cannot be null")

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py", line 16292, in save_transaction_to_database
    transaction, created = SubscriptionTransaction.objects.update_or_create(
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 949, in update_or_create
    obj, created = self.select_for_update().get_or_create(defaults, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 923, in get_or_create
    return self.create(**params), True
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 658, in create
    obj.save(force_insert=True, using=self.db)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\base.py", line 814, in save
    self.save_base(
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\base.py", line 877, in save_base
    updated = self._save_table(
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\base.py", line 1020, in _save_table
    results = self._do_insert(
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\base.py", line 1061, in _do_insert
    return manager._insert(
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\sql\compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\mysql\base.py", line 80, in execute
    raise IntegrityError(*tuple(e.args))
django.db.utils.IntegrityError: (1048, "Column 'date' cannot be null")

 INFO 2025-08-28 00:19:05,764 views 15919 Save transaction result: None 


 INFO 2025-08-28 00:19:05,765 views 15774 PROCESSED TRANSACTION CREATED: test-transaction-123 


 INFO 2025-08-28 00:19:05,765 views 15796 SubscriptionFlow webhook processed successfully 


 INFO 2025-08-28 00:19:05,776 views 15732 === SUBSCRIPTIONFLOW WEBHOOK RECEIVED === 


 INFO 2025-08-28 00:19:05,777 views 15733 Headers: {'Static-Token': 'deviareLg9VwCn6J1lVkPxcAYJqnd-academyADbybpNvp34JSrmnYatHyUme', 'Content-Length': '347', 'Content-Type': 'application/json', 'Host': '127.0.0.1:8000', 'User-Agent': 'TestScript/1.0', 'Accept-Encoding': 'gzip, deflate', 'Accept': '*/*', 'Connection': 'keep-alive'} 


 INFO 2025-08-28 00:19:05,777 views 15734 Content Type: application/json 


 INFO 2025-08-28 00:19:05,777 views 15744 Parsed JSON webhook data 


 INFO 2025-08-28 00:19:05,777 views 15763 Webhook Type: transactions 


 INFO 2025-08-28 00:19:05,778 views 15764 Webhook Event: created 


 INFO 2025-08-28 00:19:05,778 views 15765 Webhook Method: POST 


 INFO 2025-08-28 00:19:05,778 views 15766 Data Status: Pending 


 INFO 2025-08-28 00:19:05,778 views 15767 Data ID: test-transaction-json-123 


 INFO 2025-08-28 00:19:05,778 views 15768 Main data keys: ['id', 'name', 'status', 'amount', 'currency', 'customer'] 


 INFO 2025-08-28 00:19:05,778 views 15909 === PROCESSING TRANSACTION CREATED === 


 INFO 2025-08-28 00:19:05,778 views 15910 Transaction ID: test-transaction-json-123 


 INFO 2025-08-28 00:19:05,778 views 15911 Transaction Status: Pending 


 INFO 2025-08-28 00:19:05,779 views 15912 Transaction Amount: 200 USD 


 INFO 2025-08-28 00:19:05,779 views 15913 Transaction data available: True 


 INFO 2025-08-28 00:19:05,779 views 15914 Transaction data type: <class 'dict'> 


 INFO 2025-08-28 00:19:05,779 views 15917 About to save transaction to database... 


 INFO 2025-08-28 00:19:05,779 views 16232 === SAVING TRANSACTION TO DATABASE === 


 INFO 2025-08-28 00:19:05,779 views 16233 Transaction ID: test-transaction-json-123 


 INFO 2025-08-28 00:19:05,779 views 16234 Transaction Name: TEST-JSON-TRANSACTION 


 INFO 2025-08-28 00:19:05,779 views 16235 Status: Pending 


 INFO 2025-08-28 00:19:05,779 views 16236 Amount: 200 


 INFO 2025-08-28 00:19:05,780 views 16237 Currency: USD 


 INFO 2025-08-28 00:19:05,780 views 16238 Transaction data keys: ['id', 'name', 'status', 'amount', 'currency', 'customer'] 


 ERROR 2025-08-28 00:19:11,641 views 16314 ERROR: Error saving transaction to database: (1048, "Column 'date' cannot be null") 
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 916, in get_or_create
    return self.get(**kwargs), False
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 637, in get
    raise self.model.DoesNotExist(
main.models.SubscriptionTransaction.DoesNotExist: SubscriptionTransaction matching query does not exist.

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\mysql\base.py", line 75, in execute
    return self.cursor.execute(query, args)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\MySQLdb\cursors.py", line 206, in execute
    res = self._query(query)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\MySQLdb\cursors.py", line 319, in _query
    db.query(q)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\MySQLdb\connections.py", line 254, in query
    _mysql.connection.query(self, query)
MySQLdb.OperationalError: (1048, "Column 'date' cannot be null")

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py", line 16292, in save_transaction_to_database
    transaction, created = SubscriptionTransaction.objects.update_or_create(
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 949, in update_or_create
    obj, created = self.select_for_update().get_or_create(defaults, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 923, in get_or_create
    return self.create(**params), True
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 658, in create
    obj.save(force_insert=True, using=self.db)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\base.py", line 814, in save
    self.save_base(
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\base.py", line 877, in save_base
    updated = self._save_table(
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\base.py", line 1020, in _save_table
    results = self._do_insert(
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\base.py", line 1061, in _do_insert
    return manager._insert(
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\manager.py", line 87, in manager_method
    return getattr(self.get_queryset(), name)(*args, **kwargs)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\query.py", line 1805, in _insert
    return query.get_compiler(using=using).execute_sql(returning_fields)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\models\sql\compiler.py", line 1822, in execute_sql
    cursor.execute(sql, params)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\utils.py", line 102, in execute
    return super().execute(sql, params)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\utils.py", line 67, in execute
    return self._execute_with_wrappers(
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\utils.py", line 80, in _execute_with_wrappers
    return executor(sql, params, many, context)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\utils.py", line 89, in _execute
    return self.cursor.execute(sql, params)
  File "C:\Users\<USER>\Documents\enterprise\myenv\lib\site-packages\django\db\backends\mysql\base.py", line 80, in execute
    raise IntegrityError(*tuple(e.args))
django.db.utils.IntegrityError: (1048, "Column 'date' cannot be null")

 INFO 2025-08-28 00:19:11,644 views 15919 Save transaction result: None 


 INFO 2025-08-28 00:19:11,646 views 15774 PROCESSED TRANSACTION CREATED: test-transaction-json-123 


 INFO 2025-08-28 00:19:11,646 views 15796 SubscriptionFlow webhook processed successfully 


 INFO 2025-08-28 00:19:55,788 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 INFO 2025-08-28 00:20:06,344 autoreload 668 Watching for file changes with StatReloader 


 INFO 2025-08-28 00:22:53,651 views 15732 === SUBSCRIPTIONFLOW WEBHOOK RECEIVED === 


 INFO 2025-08-28 00:22:53,652 views 15733 Headers: {'Static-Token': 'deviareLg9VwCn6J1lVkPxcAYJqnd-academyADbybpNvp34JSrmnYatHyUme', 'Content-Length': '5304', 'Content-Type': 'application/x-www-form-urlencoded', 'User-Agent': 'GuzzleHttp/7', 'Host': 'webhook.site', 'Accept': '*/*', 'Accept-Encoding': 'gzip, deflate, br', 'Connection': 'keep-alive'} 


 INFO 2025-08-28 00:22:53,652 views 15734 Content Type: application/x-www-form-urlencoded 


 INFO 2025-08-28 00:22:53,657 views 15839 Successfully parsed form data into structured format 


 INFO 2025-08-28 00:22:53,658 views 15740 Parsed form-encoded webhook data 


 INFO 2025-08-28 00:22:53,658 views 15763 Webhook Type: transactions 


 INFO 2025-08-28 00:22:53,658 views 15764 Webhook Event: unknown 


 INFO 2025-08-28 00:22:53,658 views 15765 Webhook Method: unknown 


 INFO 2025-08-28 00:22:53,658 views 15766 Data Status: Pending 


 INFO 2025-08-28 00:22:53,659 views 15767 Data ID: 5717037d-b280-4b50-89ef-e6bb84788725 


 INFO 2025-08-28 00:22:53,659 views 15768 Main data keys: ['id', 'name', 'date', 'status', 'amount', 'miscellaneous_charges', 'miscellaneous_charges_breakdown', 'unapplied_amount', 'balance', 'number', 'payment_type_id', 'payment_method_id', 'cash_or_card', 'type', 'transaction_category', 'reference', 'description', 'transaction_id', 'reference_transaction_id', 'decline_reason', 'currency', 'accounting_account_code', 'created_at', 'updated_at', 'data_source', 'reason_code', 'mv_early_renewal', 'mv_renewal_plan', 'approval_link', 'customer', 'invoices'] 


 WARNING 2025-08-28 00:22:53,659 views 15776 UNHANDLED TRANSACTION EVENT: unknown 


 INFO 2025-08-28 00:22:53,659 views 15796 SubscriptionFlow webhook processed successfully 


 INFO 2025-08-28 00:23:08,064 autoreload 266 C:\Users\<USER>\Documents\enterprise\api-backend\main\views.py changed, reloading. 


 INFO 2025-08-28 09:40:08,302 tasks 4124 Created new customer: b3b7a5d7-88ff-49ad-9af6-e2f2026b1711 


 INFO 2025-08-28 09:40:10,540 tasks 4124 Created new customer: 738c6dc3-a252-487e-8f3b-c0df1040fca0 


 INFO 2025-08-28 09:40:28,304 tasks 4149 Completed syncing customers.
New: 2 | Updated: 11 | Failed: 0 | Pages: 1 | Time: 28.66s 

